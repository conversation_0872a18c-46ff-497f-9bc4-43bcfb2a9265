// 测试重定向逻辑的脚本
// 使用Node.js 18+的内置fetch API

async function testWorkspaceQuery() {
  try {
    const workspaceName = '球磨车间记录';
    console.log('🔍 测试查询工作页面:', workspaceName);
    
    const url = `http://132.232.143.210:28000/rest/v1/工作页面?页面名称=eq.${encodeURIComponent(workspaceName)}&select=页面路由`;
    console.log('🔍 查询URL:', url);
    
    const response = await fetch(url, {
      headers: {
        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiYW5vbiIsImlzcyI6InN1cGFiYXNlIiwiaWF0IjoxNzUwNjk0NDAwLCJleHAiOjE5MDg0NjA4MDB9.1wMtd68DjY3b9BM82ynEuN2oi9KfS-FJvVLROVULq7w',
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiYW5vbiIsImlzcyI6InN1cGFiYXNlIiwiaWF0IjoxNzUwNjk0NDAwLCJleHAiOjE5MDg0NjA4MDB9.1wMtd68DjY3b9BM82ynEuN2oi9KfS-FJvVLROVULq7w',
        'Content-Type': 'application/json'
      }
    });

    console.log('🔍 响应状态:', response.status);
    
    if (!response.ok) {
      console.error('❌ 查询失败:', response.status, response.statusText);
      return;
    }

    const data = await response.json();
    console.log('🔍 查询结果:', JSON.stringify(data, null, 2));

    if (data && data.length > 0 && data[0].页面路由) {
      const route = data[0].页面路由;
      console.log('✅ 找到匹配路由:', route);
    } else {
      console.log('⚠️ 未找到匹配的工作页面路由');
    }
  } catch (error) {
    console.error('❌ 查询异常:', error);
  }
}

testWorkspaceQuery();
