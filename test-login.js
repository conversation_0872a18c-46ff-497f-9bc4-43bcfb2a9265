// 测试登录API的脚本
async function testLogin() {
  try {
    console.log('🔍 测试登录API...');
    
    const response = await fetch('http://localhost:3008/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: 'bal001',
        password: 'password'
      })
    });

    console.log('🔍 响应状态:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ 登录失败:', response.status, errorText);
      return;
    }

    const data = await response.json();
    console.log('🔍 登录响应:', JSON.stringify(data, null, 2));

    if (data.success && data.redirectUrl) {
      console.log('✅ 登录成功，重定向URL:', data.redirectUrl);
      
      if (data.redirectUrl === '/ball-mill-workshop') {
        console.log('🎯 重定向验证成功！正确重定向到球磨车间页面');
      } else {
        console.log('⚠️ 重定向验证失败，期望: /ball-mill-workshop，实际:', data.redirectUrl);
      }
    } else {
      console.log('❌ 登录失败或缺少重定向URL');
    }
  } catch (error) {
    console.error('❌ 测试异常:', error);
  }
}

testLogin();
